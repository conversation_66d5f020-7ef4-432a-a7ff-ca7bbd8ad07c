import { Box, Heading, Input, Flex, Spinner, Text, VStack, InputGroup, InputLeftElement, useColorModeValue } from "@chakra-ui/react";
import { useState, useEffect } from "react";
import SuggestedUserListItem from "../components/SuggestedUserListItem"; // Import the new list item component
import useShowToast from "../hooks/useShowToast";
import { MagnifyingGlass } from "phosphor-react";
import { fetchWithSession } from "../utils/api";

// Custom scrollbar styling - will be made theme-aware in component
const getScrollbarStyles = (isDark) => ({
    "&::-webkit-scrollbar": {
        width: "6px",
    },
    "&::-webkit-scrollbar-track": {
        background: "transparent",
    },
    "&::-webkit-scrollbar-thumb": {
        background: isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)",
        borderRadius: "3px",
    },
    "&::-webkit-scrollbar-thumb:hover": {
        background: isDark ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.2)",
    },
    scrollbarWidth: "thin",
    scrollbarColor: isDark ? "rgba(255, 255, 255, 0.1) transparent" : "rgba(0, 0, 0, 0.1) transparent"
});

const SearchPage = () => {
    const [suggestedUsers, setSuggestedUsers] = useState([]);
    const [suggestedLoading, setSuggestedLoading] = useState(true);
    const showToast = useShowToast();
    const [searchQuery, setSearchQuery] = useState("");
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);

    // Theme-aware colors
    const bgColor = useColorModeValue("#f7fafc", "#101010");
    const cardBgColor = useColorModeValue("white", "#151515");
    const borderColor = useColorModeValue("rgba(0, 0, 0, 0.08)", "rgba(255, 255, 255, 0.06)");
    const textColor = useColorModeValue("gray.800", "white");
    const placeholderColor = useColorModeValue("gray.500", "gray.500");
    const spinnerColor = useColorModeValue("gray.600", "whiteAlpha.700");

    // Fetch Suggested Users
    useEffect(() => {
        const getSuggestedUsers = async () => {
            setSuggestedLoading(true);
            try {
                const res = await fetchWithSession("/api/users/suggested");
                if (res.ok) {
                    const data = await res.json();
                    if (Array.isArray(data)) {
                        setSuggestedUsers(data);
                    } else {
                        showToast("Error", "Received invalid data for suggested users", "error");
                        setSuggestedUsers([]);
                    }
                } else {
                    const errorData = await res.json().catch(() => ({ error: 'Failed to fetch suggested users' }));
                    showToast("Error", errorData.error || 'Failed to fetch suggested users', "error");
                    setSuggestedUsers([]);
                }
            } catch (error) {
                showToast("Error", error.message, "error");
                setSuggestedUsers([]);
            } finally {
                setSuggestedLoading(false);
            }
        };

        getSuggestedUsers();
    }, [showToast]);

    const handleSearchChange = async (e) => {
        const query = e.target.value;
        setSearchQuery(query);

        if (!query) {
            setSearchResults([]);
            return;
        }

        setIsSearching(true);
        try {
            const res = await fetchWithSession(`/api/users/search?query=${query}`);
            if (res.ok) {
                const data = await res.json();
                setSearchResults(data);
            } else {
                const errorData = await res.json().catch(() => ({ error: 'Failed to search users' }));
                showToast("Error", errorData.error || 'Failed to search users', "error");
                setSearchResults([]);
            }
        } catch (error) {
            showToast("Error", error.message, "error");
            setSearchResults([]);
        } finally {
            setIsSearching(false);
        }
    };

    return (
        <Box
            className="page-content-scroll"
            bg="transparent"
            pt={{ base: "60px", md: "20px" }} // Increased top padding on mobile for logo
        >
            <Heading as="h1" size="2xl" mb={8} textAlign={"center"} color={textColor}>
                Search & Discover Users
            </Heading>

            <Box
                w={["100%", "500px", "550px"]} // Increased width
                maxW="100%"
                mx="auto"
                bg={cardBgColor}
                borderRadius="2xl"
                boxShadow="none"
                borderTop={`1px solid ${borderColor}`}
                borderLeft={`1px solid ${borderColor}`}
                borderRight={`1px solid ${borderColor}`}
                borderBottom="none"
                p={5} // Increased padding
                mb={6}
            >
                <InputGroup>
                    <InputLeftElement pointerEvents="none">
                        <MagnifyingGlass color={placeholderColor} />
                    </InputLeftElement>
                    <Input
                        placeholder="Search users by username or name..."
                        value={searchQuery}
                        onChange={handleSearchChange}
                        bg="transparent"
                        border="none"
                        color={textColor}
                        _placeholder={{ color: placeholderColor }}
                        _focus={{
                            boxShadow: "none",
                            border: "none"
                        }}
                        _hover={{
                            border: "none"
                        }}
                    />
                </InputGroup>
            </Box>

            {/* Search Results Section */}
            {searchQuery && isSearching && (
                <Box
                    w={["100%", "500px", "550px"]} // Increased width
                    maxW="100%"
                    mx="auto"
                    bg={cardBgColor}
                    borderRadius="2xl"
                    boxShadow="none"
                    borderTop={`1px solid ${borderColor}`}
                    borderLeft={`1px solid ${borderColor}`}
                    borderRight={`1px solid ${borderColor}`}
                    borderBottom="none"
                    p={5} // Increased padding
                    mb={6}
                    display="flex"
                    flexDirection="column"
                    minH="120px" // Slightly increased height
                >
                    <Flex justify="center" align="center" flexGrow={1}>
                        <Spinner size="lg" color={spinnerColor} />
                    </Flex>
                </Box>
            )}

            {searchQuery && !isSearching && searchResults.length > 0 && (
                <Box
                    w={["100%", "500px", "550px"]} // Increased width
                    maxW="100%"
                    mx="auto"
                    bg={cardBgColor}
                    borderRadius="2xl"
                    boxShadow="none"
                    borderTop={`1px solid ${borderColor}`}
                    borderLeft={`1px solid ${borderColor}`}
                    borderRight={`1px solid ${borderColor}`}
                    borderBottom="none"
                    p={5} // Increased padding
                    mb={6}
                    display="flex"
                    flexDirection="column"
                    h="550px" // Further increased height for more content
                >
                    <Heading as="h2" size="lg" mb={5} textAlign={"center"} flexShrink={0} color={textColor}>
                        Search Results
                    </Heading>
                    <Box
                        overflowY="scroll" // Force scroll instead of auto
                        flexGrow={1}
                        h="calc(100% - 60px)" // Subtract the heading height (adjusted for larger heading)
                        className="always-show-scrollbar" // Add a class for custom styling
                        css={{
                            '&::-webkit-scrollbar': {
                                width: '8px', // Wider scrollbar
                                display: 'block', // Always show scrollbar
                            },
                            '&::-webkit-scrollbar-track': {
                                background: useColorModeValue('#f0f0f0', '#1a1a1a'), // Theme-aware track
                                display: 'block', // Always show track
                            },
                            '&::-webkit-scrollbar-thumb': {
                                background: useColorModeValue('rgba(0, 0, 0, 0.3)', 'rgba(255, 255, 255, 0.3)'), // Theme-aware thumb
                                borderRadius: '4px',
                                minHeight: '30px', // Ensure thumb is visible
                            },
                            '&::-webkit-scrollbar-thumb:hover': {
                                background: useColorModeValue('rgba(0, 0, 0, 0.5)', 'rgba(255, 255, 255, 0.5)'), // Theme-aware hover
                            },
                            scrollbarWidth: 'thin',
                            scrollbarColor: useColorModeValue('rgba(0, 0, 0, 0.3) #f0f0f0', 'rgba(255, 255, 255, 0.3) #1a1a1a'),
                            // Firefox specific
                            scrollbarGutter: 'stable',
                        }}
                    >
                        <VStack spacing={6} align="stretch" pb={6}>
                            {/* Render actual search results */}
                            {searchResults.map((user) => (
                                <SuggestedUserListItem key={user._id} user={user} />
                            ))}

                            {/* Add padding at the bottom for spacing */}
                            <Box h="20px"></Box>
                        </VStack>
                    </Box>
                </Box>
            )}

            {searchQuery && !isSearching && searchResults.length === 0 && (
                <Box
                    w={["100%", "500px", "550px"]} // Increased width
                    maxW="100%"
                    mx="auto"
                    bg={cardBgColor}
                    borderRadius="2xl"
                    boxShadow="none"
                    borderTop={`1px solid ${borderColor}`}
                    borderLeft={`1px solid ${borderColor}`}
                    borderRight={`1px solid ${borderColor}`}
                    borderBottom="none"
                    p={5} // Increased padding
                    mb={6}
                    display="flex"
                    flexDirection="column"
                    minH="120px" // Slightly increased height
                >
                    <Flex align="center" justify="center" flexGrow={1}>
                        <Text textAlign={"center"} color={textColor}>No users found matching "{searchQuery}"</Text>
                    </Flex>
                </Box>
            )}

            {/* Suggested Users Section */}
            {!searchQuery && (
                <Box
                    w={["100%", "500px", "550px"]} // Increased width
                    maxW="100%"
                    mx="auto"
                    bg={cardBgColor}
                    borderRadius="2xl"
                    boxShadow="none"
                    borderTop={`1px solid ${borderColor}`}
                    borderLeft={`1px solid ${borderColor}`}
                    borderRight={`1px solid ${borderColor}`}
                    borderBottom="none"
                    p={5} // Increased padding
                    mb={6}
                    display="flex"
                    flexDirection="column"
                    h="550px" // Further increased height for more content
                >
                    <Heading as="h2" size="lg" mb={5} textAlign={"center"} flexShrink={0} color={textColor}>
                        Suggested Users
                    </Heading>
                    {suggestedLoading ? (
                        <Flex justify="center" p={4} flexGrow={1}>
                            <Spinner size="lg" color={spinnerColor} />
                        </Flex>
                    ) : suggestedUsers.length === 0 ? (
                        <Text textAlign={"center"} p={4} flexGrow={1} color={textColor}>No suggested users available.</Text>
                    ) : (
                        <Box
                            overflowY="scroll" // Force scroll instead of auto
                            flexGrow={1}
                            h="calc(100% - 60px)" // Subtract the heading height (adjusted for larger heading)
                            className="always-show-scrollbar" // Add a class for custom styling
                            css={{
                                '&::-webkit-scrollbar': {
                                    width: '8px', // Wider scrollbar
                                    display: 'block', // Always show scrollbar
                                },
                                '&::-webkit-scrollbar-track': {
                                    background: useColorModeValue('#f0f0f0', '#1a1a1a'), // Theme-aware track
                                    display: 'block', // Always show track
                                },
                                '&::-webkit-scrollbar-thumb': {
                                    background: useColorModeValue('rgba(0, 0, 0, 0.3)', 'rgba(255, 255, 255, 0.3)'), // Theme-aware thumb
                                    borderRadius: '4px',
                                    minHeight: '30px', // Ensure thumb is visible
                                },
                                '&::-webkit-scrollbar-thumb:hover': {
                                    background: useColorModeValue('rgba(0, 0, 0, 0.5)', 'rgba(255, 255, 255, 0.5)'), // Theme-aware hover
                                },
                                scrollbarWidth: 'thin',
                                scrollbarColor: useColorModeValue('rgba(0, 0, 0, 0.3) #f0f0f0', 'rgba(255, 255, 255, 0.3) #1a1a1a'),
                                // Firefox specific
                                scrollbarGutter: 'stable',
                            }}
                        >
                            <VStack spacing={6} align="stretch" pb={6}>
                                {/* Render actual suggested users */}
                                {suggestedUsers.map((user) => (
                                    <SuggestedUserListItem key={user._id} user={user} />
                                ))}

                                {/* Add padding at the bottom for spacing */}
                                <Box h="20px"></Box>
                            </VStack>
                        </Box>
                    )}
                </Box>
            )}
        </Box>
    );
};

export default SearchPage;
