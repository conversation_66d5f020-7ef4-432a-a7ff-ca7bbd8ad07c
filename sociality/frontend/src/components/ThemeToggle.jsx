import { 
  Box, 
  Flex, 
  Text, 
  Switch, 
  useColorModeValue,
  Icon
} from "@chakra-ui/react";
import { Sun, Moon } from "phosphor-react";
import useTheme from "../hooks/useTheme";

const ThemeToggle = () => {
  const { theme, toggleTheme, isDark } = useTheme();
  
  // Theme-aware colors
  const bgColor = useColorModeValue("white", "#1a1a1a");
  const borderColor = useColorModeValue("rgba(0, 0, 0, 0.08)", "rgba(255, 255, 255, 0.08)");
  const textColor = useColorModeValue("gray.800", "white");
  const descriptionColor = useColorModeValue("gray.600", "gray.400");
  const iconColor = useColorModeValue("#f6ad55", "#ffd700");

  return (
    <Box
      width="100%"
      p={6}
      borderWidth="1px"
      borderRadius="xl"
      borderColor={borderColor}
      bg={bgColor}
      boxShadow={useColorModeValue("0 4px 12px rgba(0, 0, 0, 0.05)", "0 4px 12px rgba(0, 0, 0, 0.2)")}
      className="threads-post-card"
    >
      <Text fontSize="xl" fontWeight="bold" mb={5} color={textColor}>
        Appearance
      </Text>

      <Flex justify="space-between" align="center">
        <Box>
          <Flex align="center" mb={1}>
            <Icon 
              as={isDark ? Moon : Sun} 
              size={20} 
              color={iconColor}
              mr={2}
            />
            <Text fontWeight="bold" color={textColor}>
              {isDark ? "Dark Mode" : "Light Mode"}
            </Text>
          </Flex>
          <Text fontSize="sm" color={descriptionColor}>
            {isDark 
              ? "Switch to light mode for a brighter interface" 
              : "Switch to dark mode for a darker interface"
            }
          </Text>
        </Box>
        
        <Switch
          size="lg"
          isChecked={isDark}
          onChange={toggleTheme}
          colorScheme="brand"
          sx={{
            '& .chakra-switch__track': {
              bg: isDark ? 'brand.primary.500' : 'gray.300',
              _checked: {
                bg: 'brand.primary.500',
              },
            },
            '& .chakra-switch__thumb': {
              bg: 'white',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
            },
          }}
        />
      </Flex>
    </Box>
  );
};

export default ThemeToggle;
