/**
 * Theme utility functions
 * Handles applying theme classes to the document root
 */

/**
 * Apply theme to document root
 * @param {string} theme - 'light' or 'dark'
 */
export const applyThemeToDocument = (theme) => {
  const root = document.documentElement;
  
  if (theme === 'light') {
    root.setAttribute('data-theme', 'light');
  } else {
    root.removeAttribute('data-theme');
  }
};

/**
 * Get current theme from document
 * @returns {string} - 'light' or 'dark'
 */
export const getCurrentThemeFromDocument = () => {
  const root = document.documentElement;
  return root.getAttribute('data-theme') === 'light' ? 'light' : 'dark';
};
