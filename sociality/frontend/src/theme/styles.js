/**
 * Global styles configuration
 * Contains global style definitions for the application
 * Supports both light and dark modes
 */
export const styles = {
  global: (props) => ({
    body: {
      color: props.colorMode === 'dark' ? "whiteAlpha.900" : "gray.800",
      bg: props.colorMode === 'dark' ? "#101010" : "#f7fafc",
      margin: 0,
      padding: 0,
      transition: "background-color 0.2s, color 0.2s",
    },
    html: {
      margin: 0,
      padding: 0,
    },
  }),
};
